// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kjykeipekjbmyvpriear.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtqeWtlaXBla2pibXl2cHJpZWFyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MTQ5ODAsImV4cCI6MjA2NjI5MDk4MH0.oSuSPtjCvBBLxUBdAp6J0kfjGhVzR5vdmWCeGsnHAB0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);