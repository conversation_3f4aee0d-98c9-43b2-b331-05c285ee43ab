export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      availabilities: {
        Row: {
          created_at: string
          day_of_week: Database["public"]["Enums"]["day_of_week"]
          id: string
          time_slot: string
          user_id: string
        }
        Insert: {
          created_at?: string
          day_of_week: Database["public"]["Enums"]["day_of_week"]
          id?: string
          time_slot: string
          user_id: string
        }
        Update: {
          created_at?: string
          day_of_week?: Database["public"]["Enums"]["day_of_week"]
          id?: string
          time_slot?: string
          user_id?: string
        }
        Relationships: []
      }
      group_members: {
        Row: {
          group_id: string
          id: string
          joined_at: string
          user_id: string
        }
        Insert: {
          group_id: string
          id?: string
          joined_at?: string
          user_id: string
        }
        Update: {
          group_id?: string
          id?: string
          joined_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          admin_note: string | null
          bundesland: string
          created_at: string
          id: string
          klassenstufe: string
          link_sent_at: string | null
          max_students: number
          status: Database["public"]["Enums"]["group_status"]
          time_slots: string[]
          updated_at: string
          whatsapp_link: string | null
        }
        Insert: {
          admin_note?: string | null
          bundesland: string
          created_at?: string
          id?: string
          klassenstufe: string
          link_sent_at?: string | null
          max_students?: number
          status?: Database["public"]["Enums"]["group_status"]
          time_slots: string[]
          updated_at?: string
          whatsapp_link?: string | null
        }
        Update: {
          admin_note?: string | null
          bundesland?: string
          created_at?: string
          id?: string
          klassenstufe?: string
          link_sent_at?: string | null
          max_students?: number
          status?: Database["public"]["Enums"]["group_status"]
          time_slots?: string[]
          updated_at?: string
          whatsapp_link?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          bundesland: string
          created_at: string
          email: string
          first_name: string
          id: string
          is_admin: boolean
          klassenstufe: string
          last_name: string
          sessions_per_month: number
          updated_at: string
        }
        Insert: {
          bundesland: string
          created_at?: string
          email: string
          first_name: string
          id: string
          is_admin?: boolean
          klassenstufe: string
          last_name: string
          sessions_per_month?: number
          updated_at?: string
        }
        Update: {
          bundesland?: string
          created_at?: string
          email?: string
          first_name?: string
          id?: string
          is_admin?: boolean
          klassenstufe?: string
          last_name?: string
          sessions_per_month?: number
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_auto_group: {
        Args: {
          p_bundesland: string
          p_klassenstufe: string
          p_student_ids: string[]
          p_time_slots: string[]
        }
        Returns: string
      }
      find_compatible_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          bundesland: string
          klassenstufe: string
          matching_students: Json
          common_time_slots: string[]
          student_count: number
        }[]
      }
      find_matching_groups: {
        Args: { user_uuid: string }
        Returns: {
          group_id: string
          bundesland: string
          klassenstufe: string
          matching_slots: number
          current_members: number
        }[]
      }
      get_active_groups: {
        Args: Record<PropertyKey, never>
        Returns: {
          group_id: string
          bundesland: string
          klassenstufe: string
          time_slots: string[]
          student_count: number
          whatsapp_link: string
          created_at: string
          status: string
        }[]
      }
      get_pending_groups_with_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          group_id: string
          bundesland: string
          klassenstufe: string
          time_slots: string[]
          student_count: number
          students: Json
        }[]
      }
      is_current_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      process_auto_matching: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_group_id: string
          bundesland: string
          klassenstufe: string
          student_count: number
          common_slots: string[]
        }[]
      }
    }
    Enums: {
      day_of_week:
        | "monday"
        | "tuesday"
        | "wednesday"
        | "thursday"
        | "friday"
        | "saturday"
        | "sunday"
      group_status: "pending" | "active" | "completed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      day_of_week: [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ],
      group_status: ["pending", "active", "completed"],
    },
  },
} as const
